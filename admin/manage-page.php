<?php

/**
 * Git Manager Manage Page
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check user permissions
if (!current_user_can('manage_options')) {
    wp_die(__('You do not have sufficient permissions to access this page.'));
}

// Get current tab
$current_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'files';

// Define tabs
$tabs = array(
    'files' => 'File Management',
    'commits' => 'Commits',
    'branches' => 'Branches',
    'remotes' => 'Remotes',
    'history' => 'History',
    'advanced' => 'Advanced'
);

// Get Git operations class
include_once plugin_dir_path(__FILE__) . '../includes/class-git-operations.php';
$git_ops = new WPGitManager_GitOperations();

// Get repository information
$git_path = get_option('wpgm_git_path', '/usr/bin/git');
$repo_path = get_option('wpgm_repo_path', ABSPATH);
$status = $git_ops->get_status($git_path, $repo_path);

?>

<div class="wrap">
    <h1>Git Manager - Manage Repository</h1>

    <!-- Tab Navigation -->
    <nav class="nav-tab-wrapper">
        <?php foreach ($tabs as $tab_key => $tab_name): ?>
            <a href="<?php echo admin_url('admin.php?page=wp-git-manager-manage&tab=' . $tab_key); ?>"
                class="nav-tab <?php echo $current_tab === $tab_key ? 'nav-tab-active' : ''; ?>">
                <?php echo esc_html($tab_name); ?>
            </a>
        <?php endforeach; ?>
    </nav>

    <div class="tab-content">

        <?php if ($current_tab === 'files'): ?>
            <!-- File Management Tab -->
            <div class="tab-pane active">
                <h2>File Management</h2>

                <div class="git-actions-bar">
                    <button type="button" class="button button-primary git-commit-btn">
                        <span class="dashicons dashicons-yes"></span> Commit Changes
                    </button>
                    <button type="button" class="button" id="refresh-status">
                        <span class="dashicons dashicons-update"></span> Refresh Status
                    </button>
                    <button type="button" class="button" id="stage-all">
                        <span class="dashicons dashicons-plus"></span> Stage All
                    </button>
                    <button type="button" class="button" id="unstage-all">
                        <span class="dashicons dashicons-minus"></span> Unstage All
                    </button>
                </div>

                <div id="git-file-status">
                    <?php if (!empty($status['files'])): ?>
                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr>
                                    <th scope="col" class="manage-column column-cb check-column">
                                        <input type="checkbox" id="select-all-files">
                                    </th>
                                    <th scope="col" class="manage-column">Status</th>
                                    <th scope="col" class="manage-column">File</th>
                                    <th scope="col" class="manage-column">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($status['files'] as $file): ?>
                                    <tr>
                                        <th scope="row" class="check-column">
                                            <input type="checkbox" name="selected_files[]" value="<?php echo esc_attr($file['name']); ?>">
                                        </th>
                                        <td>
                                            <span class="file-status status-<?php echo esc_attr(strtolower($file['status'])); ?>">
                                                <?php echo esc_html($file['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <code><?php echo esc_html($file['name']); ?></code>
                                        </td>
                                        <td>
                                            <button type="button" class="button button-small git-stage-file" data-file="<?php echo esc_attr($file['name']); ?>">
                                                Stage
                                            </button>
                                            <button type="button" class="button button-small git-unstage-file" data-file="<?php echo esc_attr($file['name']); ?>">
                                                Unstage
                                            </button>
                                            <button type="button" class="button button-small git-view-diff" data-file="<?php echo esc_attr($file['name']); ?>">
                                                View Diff
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <div class="notice notice-info">
                            <p>No changes detected in the repository.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        <?php elseif ($current_tab === 'commits'): ?>
            <!-- Commits Tab -->
            <div class="tab-pane active">
                <h2>Commit Operations</h2>

                <div class="commit-form">
                    <h3>Create New Commit</h3>
                    <table class="form-table">
                        <tr>
                            <th scope="row">Commit Message</th>
                            <td>
                                <textarea id="commit-message" rows="3" cols="50" class="large-text" placeholder="Enter commit message..."></textarea>
                                <p class="description">Describe the changes you're committing.</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Options</th>
                            <td>
                                <label>
                                    <input type="checkbox" id="auto-stage" checked>
                                    Automatically stage all changes
                                </label>
                            </td>
                        </tr>
                    </table>
                    <p class="submit">
                        <button type="button" class="button button-primary" id="create-commit">
                            <span class="dashicons dashicons-yes"></span> Create Commit
                        </button>
                    </p>
                </div>

                <div class="push-pull-actions">
                    <h3>Sync with Remote</h3>
                    <p>
                        <button type="button" class="button git-push-btn">
                            <span class="dashicons dashicons-upload"></span> Push to Remote
                        </button>
                        <button type="button" class="button git-pull-btn">
                            <span class="dashicons dashicons-download"></span> Pull from Remote
                        </button>
                    </p>
                </div>
            </div>

        <?php elseif ($current_tab === 'branches'): ?>
            <!-- Branches Tab -->
            <div class="tab-pane active">
                <h2>Branch Management</h2>

                <div class="branch-actions">
                    <h3>Create New Branch</h3>
                    <table class="form-table">
                        <tr>
                            <th scope="row">Branch Name</th>
                            <td>
                                <input type="text" id="new-branch-name" class="regular-text" placeholder="feature/new-feature">
                                <button type="button" class="button" id="create-branch">Create Branch</button>
                                <p class="description">Enter a name for the new branch.</p>
                            </td>
                        </tr>
                    </table>
                </div>

                <div id="branches-list">
                    <h3>Existing Branches</h3>
                    <div id="branch-list-content">
                        <button type="button" class="button" id="load-branches">Load Branches</button>
                    </div>
                </div>
            </div>

        <?php elseif ($current_tab === 'remotes'): ?>
            <!-- Remotes Tab -->
            <div class="tab-pane active">
                <h2>Remote Repository Management</h2>

                <div class="remote-actions">
                    <h3>Add New Remote</h3>
                    <table class="form-table">
                        <tr>
                            <th scope="row">Remote Name</th>
                            <td>
                                <input type="text" id="remote-name" class="regular-text" value="origin" placeholder="origin">
                                <p class="description">Name for the remote repository (e.g., origin).</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Remote URL</th>
                            <td>
                                <input type="url" id="remote-url" class="large-text" placeholder="https://github.com/username/repository.git">
                                <p class="description">URL of the remote repository.</p>
                            </td>
                        </tr>
                    </table>
                    <p class="submit">
                        <button type="button" class="button button-primary" id="add-remote">
                            <span class="dashicons dashicons-plus"></span> Add Remote
                        </button>
                    </p>
                </div>

                <div id="remotes-list">
                    <h3>Existing Remotes</h3>
                    <div id="remote-list-content">
                        <button type="button" class="button" id="load-remotes">Load Remotes</button>
                    </div>
                </div>
            </div>

        <?php elseif ($current_tab === 'history'): ?>
            <!-- History Tab -->
            <div class="tab-pane active">
                <h2>Commit History</h2>

                <div class="history-controls">
                    <label for="history-limit">Show last:</label>
                    <select id="history-limit">
                        <option value="10">10 commits</option>
                        <option value="25" selected>25 commits</option>
                        <option value="50">50 commits</option>
                        <option value="100">100 commits</option>
                    </select>
                    <button type="button" class="button" id="load-history">Load History</button>
                </div>

                <div id="commit-history">
                    <p>Click "Load History" to view commit history.</p>
                </div>
            </div>

        <?php elseif ($current_tab === 'advanced'): ?>
            <!-- Advanced Tab -->
            <div class="tab-pane active">
                <h2>Advanced Git Operations</h2>

                <div class="advanced-actions">
                    <h3>Repository Information</h3>
                    <p>
                        <button type="button" class="button" id="view-git-log">View Git Log</button>
                        <button type="button" class="button" id="view-git-status">View Git Status</button>
                        <button type="button" class="button" id="view-git-config">View Git Config</button>
                    </p>

                    <h3>Maintenance</h3>
                    <p>
                        <button type="button" class="button" id="git-gc">Garbage Collection</button>
                        <button type="button" class="button" id="git-fsck">File System Check</button>
                    </p>

                    <div class="danger-zone" style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin-top: 20px;">
                        <h4 style="margin-top: 0; color: #856404;">⚠️ Danger Zone</h4>
                        <p style="color: #856404;">These operations can be destructive. Use with caution.</p>
                        <p>
                            <button type="button" class="button button-secondary" id="reset-hard">Hard Reset</button>
                            <button type="button" class="button button-secondary" id="clean-untracked">Clean Untracked Files</button>
                        </p>
                    </div>
                </div>
            </div>

        <?php endif; ?>

    </div>

    <!-- Output Area -->
    <div id="git-output" style="margin-top: 20px;"></div>

</div>

<style>
    .tab-content {
        margin-top: 20px;
    }

    .git-actions-bar {
        margin-bottom: 20px;
        padding: 15px;
        background: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .git-actions-bar .button {
        margin-right: 10px;
    }

    .file-status {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 3px;
        font-weight: bold;
        font-size: 12px;
        color: white;
    }

    .status-m {
        background-color: #d63638;
    }

    /* Modified */
    .status-a {
        background-color: #00a32a;
    }

    /* Added */
    .status-d {
        background-color: #d63638;
    }

    /* Deleted */
    .status-r {
        background-color: #dba617;
    }

    /* Renamed */
    .status-\? {
        background-color: #666;
    }

    /* Untracked */

    .commit-form,
    .branch-actions,
    .remote-actions,
    .advanced-actions {
        background: #fff;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .history-controls {
        margin-bottom: 20px;
    }

    .history-controls select,
    .history-controls button {
        margin-left: 10px;
    }

    #commit-history .commit-item {
        border-bottom: 1px solid #eee;
        padding: 10px 0;
    }

    #commit-history .commit-item:last-child {
        border-bottom: none;
    }

    .danger-zone .button {
        margin-right: 10px;
    }
</style>

<script type="text/javascript">
    jQuery(document).ready(function($) {

        // File management
        $('#select-all-files').on('change', function() {
            const isChecked = $(this).is(':checked');
            $('input[name="selected_files[]"]').prop('checked', isChecked);
        });

        $(document).on('click', '.git-stage-file', function(e) {
            e.preventDefault();
            const file = $(this).data('file');
            stageFile(file);
        });

        $(document).on('click', '.git-unstage-file', function(e) {
            e.preventDefault();
            const file = $(this).data('file');
            unstageFile(file);
        });

        $(document).on('click', '.git-view-diff', function(e) {
            e.preventDefault();
            const file = $(this).data('file');
            viewDiff(file);
        });

        $('#stage-all').on('click', function(e) {
            e.preventDefault();
            stageAll();
        });

        $('#unstage-all').on('click', function(e) {
            e.preventDefault();
            unstageAll();
        });

        $('#refresh-status').on('click', function(e) {
            e.preventDefault();
            location.reload();
        });

        // Commit operations
        $('#create-commit').on('click', function(e) {
            e.preventDefault();
            const message = $('#commit-message').val();
            const autoStage = $('#auto-stage').is(':checked');

            if (!message.trim()) {
                alert('Please enter a commit message.');
                return;
            }

            createCommit(message, autoStage);
        });

        // Branch management
        $('#create-branch').on('click', function(e) {
            e.preventDefault();
            const branchName = $('#new-branch-name').val();

            if (!branchName.trim()) {
                alert('Please enter a branch name.');
                return;
            }

            createBranch(branchName);
        });

        $('#load-branches').on('click', function(e) {
            e.preventDefault();
            loadBranches();
        });

        // Remote management
        $('#add-remote').on('click', function(e) {
            e.preventDefault();
            const name = $('#remote-name').val();
            const url = $('#remote-url').val();

            if (!name.trim() || !url.trim()) {
                alert('Please enter both remote name and URL.');
                return;
            }

            addRemote(name, url);
        });

        $('#load-remotes').on('click', function(e) {
            e.preventDefault();
            loadRemotes();
        });

        // History
        $('#load-history').on('click', function(e) {
            e.preventDefault();
            const limit = $('#history-limit').val();
            loadHistory(limit);
        });

        // Advanced operations
        $('#view-git-log').on('click', function(e) {
            e.preventDefault();
            executeGitCommand('log --oneline -10');
        });

        $('#view-git-status').on('click', function(e) {
            e.preventDefault();
            executeGitCommand('status');
        });

        $('#view-git-config').on('click', function(e) {
            e.preventDefault();
            executeGitCommand('config --list');
        });

        $('#git-gc').on('click', function(e) {
            e.preventDefault();
            if (confirm('Run garbage collection? This will optimize the repository.')) {
                executeGitCommand('gc');
            }
        });

        $('#git-fsck').on('click', function(e) {
            e.preventDefault();
            if (confirm('Run file system check? This will verify repository integrity.')) {
                executeGitCommand('fsck');
            }
        });

        $('#reset-hard').on('click', function(e) {
            e.preventDefault();
            if (confirm('WARNING: This will permanently discard all uncommitted changes. Are you sure?')) {
                executeGitCommand('reset --hard HEAD');
            }
        });

        $('#clean-untracked').on('click', function(e) {
            e.preventDefault();
            if (confirm('WARNING: This will permanently delete all untracked files. Are you sure?')) {
                executeGitCommand('clean -fd');
            }
        });

        // Push/Pull buttons
        $('.git-push-btn').on('click', function(e) {
            e.preventDefault();
            showLoading('Pushing to remote...');

            $.post(ajaxurl, {
                action: 'git_push',
                nonce: wpGitManager.nonce
            }, function(response) {
                hideLoading();
                if (response.success) {
                    showMessage('Push successful', 'success');
                } else {
                    showMessage('Push failed: ' + response.data, 'error');
                }
            });
        });

        $('.git-pull-btn').on('click', function(e) {
            e.preventDefault();
            showLoading('Pulling from remote...');

            $.post(ajaxurl, {
                action: 'git_pull',
                nonce: wpGitManager.nonce
            }, function(response) {
                hideLoading();
                if (response.success) {
                    showMessage('Pull successful', 'success');
                    location.reload();
                } else {
                    showMessage('Pull failed: ' + response.data, 'error');
                }
            });
        });

        $('.git-commit-btn').on('click', function(e) {
            e.preventDefault();
            const message = prompt('Enter commit message:');
            if (message) {
                createCommit(message, true);
            }
        });

        // API functions
        function stageFile(file) {
            showLoading('Staging file...');

            $.post(ajaxurl, {
                action: 'git_stage_file',
                file: file,
                nonce: wpGitManager.nonce
            }, function(response) {
                hideLoading();
                if (response.success) {
                    showMessage('File staged successfully', 'success');
                    location.reload();
                } else {
                    showMessage('Error staging file: ' + response.data, 'error');
                }
            });
        }

        function unstageFile(file) {
            showLoading('Unstaging file...');

            $.post(ajaxurl, {
                action: 'git_unstage_file',
                file: file,
                nonce: wpGitManager.nonce
            }, function(response) {
                hideLoading();
                if (response.success) {
                    showMessage('File unstaged successfully', 'success');
                    location.reload();
                } else {
                    showMessage('Error unstaging file: ' + response.data, 'error');
                }
            });
        }

        function stageAll() {
            showLoading('Staging all files...');

            $.post(ajaxurl, {
                action: 'git_stage_all',
                nonce: wpGitManager.nonce
            }, function(response) {
                hideLoading();
                if (response.success) {
                    showMessage('All files staged successfully', 'success');
                    location.reload();
                } else {
                    showMessage('Error staging files: ' + response.data, 'error');
                }
            });
        }

        function unstageAll() {
            showLoading('Unstaging all files...');

            $.post(ajaxurl, {
                action: 'git_unstage_all',
                nonce: wpGitManager.nonce
            }, function(response) {
                hideLoading();
                if (response.success) {
                    showMessage('All files unstaged successfully', 'success');
                    location.reload();
                } else {
                    showMessage('Error unstaging files: ' + response.data, 'error');
                }
            });
        }

        function viewDiff(file) {
            showLoading('Loading diff...');

            $.post(ajaxurl, {
                action: 'git_file_diff',
                file: file,
                nonce: wpGitManager.nonce
            }, function(response) {
                hideLoading();
                if (response.success) {
                    showDiff(file, response.data.diff);
                } else {
                    showMessage('Error loading diff: ' + response.data, 'error');
                }
            });
        }

        function createCommit(message, autoStage) {
            showLoading('Creating commit...');

            $.post(ajaxurl, {
                action: 'git_commit',
                message: message,
                auto_stage: autoStage,
                nonce: wpGitManager.nonce
            }, function(response) {
                hideLoading();
                if (response.success) {
                    showMessage('Commit created successfully', 'success');
                    $('#commit-message').val('');
                    location.reload();
                } else {
                    showMessage('Error creating commit: ' + response.data, 'error');
                }
            });
        }

        function createBranch(branchName) {
            showLoading('Creating branch...');

            $.post(ajaxurl, {
                action: 'git_create_branch',
                branch_name: branchName,
                nonce: wpGitManager.nonce
            }, function(response) {
                hideLoading();
                if (response.success) {
                    showMessage('Branch created successfully', 'success');
                    $('#new-branch-name').val('');
                    loadBranches();
                } else {
                    showMessage('Error creating branch: ' + response.data, 'error');
                }
            });
        }

        function loadBranches() {
            showLoading('Loading branches...');

            $.post(ajaxurl, {
                action: 'git_get_branches',
                nonce: wpGitManager.nonce
            }, function(response) {
                hideLoading();
                if (response.success) {
                    displayBranches(response.data);
                } else {
                    showMessage('Error loading branches: ' + response.data, 'error');
                }
            });
        }

        function addRemote(name, url) {
            showLoading('Adding remote...');

            $.post(ajaxurl, {
                action: 'git_add_remote',
                remote_name: name,
                remote_url: url,
                nonce: wpGitManager.nonce
            }, function(response) {
                hideLoading();
                if (response.success) {
                    showMessage('Remote added successfully', 'success');
                    $('#remote-name').val('');
                    $('#remote-url').val('');
                    loadRemotes();
                } else {
                    showMessage('Error adding remote: ' + response.data, 'error');
                }
            });
        }

        function loadRemotes() {
            showLoading('Loading remotes...');

            $.post(ajaxurl, {
                action: 'git_get_remotes',
                nonce: wpGitManager.nonce
            }, function(response) {
                hideLoading();
                if (response.success) {
                    displayRemotes(response.data);
                } else {
                    showMessage('Error loading remotes: ' + response.data, 'error');
                }
            });
        }

        function loadHistory(limit) {
            showLoading('Loading commit history...');

            $.post(ajaxurl, {
                action: 'git_commit_history',
                limit: limit,
                nonce: wpGitManager.nonce
            }, function(response) {
                hideLoading();
                if (response.success) {
                    displayHistory(response.data.commits);
                } else {
                    showMessage('Error loading history: ' + response.data, 'error');
                }
            });
        }

        function executeGitCommand(command) {
            showLoading('Executing command...');

            $.post(ajaxurl, {
                action: 'git_command',
                git_command: command,
                nonce: wpGitManager.nonce
            }, function(response) {
                hideLoading();
                if (response.success) {
                    showOutput(command, response.data.message);
                } else {
                    showMessage('Error executing command: ' + response.data, 'error');
                }
            });
        }

        // UI helper functions
        function showLoading(message) {
            $('#git-output').html('<div class="notice notice-info"><p>' + message + '</p></div>');
        }

        function hideLoading() {
            // Loading will be replaced by actual content
        }

        function showMessage(message, type) {
            const className = type === 'success' ? 'notice-success' : 'notice-error';
            $('#git-output').html('<div class="notice ' + className + ' is-dismissible"><p>' + message + '</p></div>');
        }

        function showOutput(command, output) {
            const html = '<div class="git-command-output">' +
                '<h4>Command: git ' + command + '</h4>' +
                '<pre>' + output + '</pre>' +
                '</div>';
            $('#git-output').html(html);
        }

        function showDiff(file, diff) {
            const html = '<div class="git-diff-output">' +
                '<h4>Diff for: ' + file + '</h4>' +
                '<pre class="diff-content">' + diff + '</pre>' +
                '</div>';
            $('#git-output').html(html);
        }

        function displayBranches(data) {
            let html = '<table class="wp-list-table widefat fixed striped">';
            html += '<thead><tr><th>Branch</th><th>Current</th><th>Actions</th></tr></thead><tbody>';

            if (data.branches && data.branches.length > 0) {
                data.branches.forEach(function(branch) {
                    const isCurrent = branch === data.current_branch;
                    html += '<tr>';
                    html += '<td><code>' + branch + '</code></td>';
                    html += '<td>' + (isCurrent ? '<strong>✓ Current</strong>' : '') + '</td>';
                    html += '<td>';
                    if (!isCurrent) {
                        html += '<button class="button button-small git-switch-branch" data-branch="' + branch + '">Switch</button>';
                    }
                    html += '</td>';
                    html += '</tr>';
                });
            } else {
                html += '<tr><td colspan="3">No branches found</td></tr>';
            }

            html += '</tbody></table>';
            $('#branch-list-content').html(html);

            // Bind switch branch events
            $('.git-switch-branch').on('click', function(e) {
                e.preventDefault();
                const branch = $(this).data('branch');
                if (confirm('Switch to branch: ' + branch + '?')) {
                    switchBranch(branch);
                }
            });
        }

        function displayRemotes(remotes) {
            let html = '<table class="wp-list-table widefat fixed striped">';
            html += '<thead><tr><th>Name</th><th>URL</th><th>Actions</th></tr></thead><tbody>';

            if (remotes && remotes.length > 0) {
                remotes.forEach(function(remote) {
                    html += '<tr>';
                    html += '<td><strong>' + remote.name + '</strong></td>';
                    html += '<td><code>' + remote.url + '</code></td>';
                    html += '<td><button class="button button-small button-link-delete">Remove</button></td>';
                    html += '</tr>';
                });
            } else {
                html += '<tr><td colspan="3">No remotes configured</td></tr>';
            }

            html += '</tbody></table>';
            $('#remote-list-content').html(html);
        }

        function displayHistory(commits) {
            let html = '';

            if (commits && commits.length > 0) {
                commits.forEach(function(commit) {
                    html += '<div class="commit-item">';

                    // Handle both string format (from git log --oneline) and object format
                    if (typeof commit === 'string') {
                        // Parse string format like "f9a7a9d Initial commit"
                        const spaceIndex = commit.indexOf(' ');
                        const hash = spaceIndex > 0 ? commit.substring(0, spaceIndex) : commit.substring(0, 8);
                        const message = spaceIndex > 0 ? commit.substring(spaceIndex + 1) : 'No message';

                        html += '<div class="commit-hash"><code>' + hash + '</code></div>';
                        html += '<div class="commit-message">' + message + '</div>';
                    } else {
                        // Handle object format with hash, message, author, date properties
                        html += '<div class="commit-hash"><code>' + commit.hash.substring(0, 8) + '</code></div>';
                        html += '<div class="commit-message">' + commit.message + '</div>';
                        html += '<div class="commit-meta">';
                        html += '<span class="commit-author">' + commit.author + '</span>';
                        html += '<span class="commit-date">' + commit.date + '</span>';
                        html += '</div>';
                    }

                    html += '</div>';
                });
            } else {
                html = '<p>No commits found in repository.</p>';
            }

            $('#commit-history').html(html);
        }

        function switchBranch(branch) {
            showLoading('Switching branch...');

            $.post(ajaxurl, {
                action: 'git_switch_branch',
                branch: branch,
                nonce: wpGitManager.nonce
            }, function(response) {
                hideLoading();
                if (response.success) {
                    showMessage('Switched to branch: ' + branch, 'success');
                    loadBranches();
                } else {
                    showMessage('Error switching branch: ' + response.data, 'error');
                }
            });
        }
    });
</script>